{"name": "tn-contracts", "version": "1.0.0", "description": "Telcoin Contracts", "type": "module", "module": "ESNext", "main": "index.js", "directories": {"test": "test"}, "scripts": {"build": "tsc", "start": "node node/dist/subscriber.js", "subscriber": "npm run build && node node/dist/relay/subscriber.js", "includer": "npm run build && node node/dist/relay/includer.js", "initiate": "npm run build && node node/dist/relay/devnet/initiate.js", "verify": "npm run build && node node/dist/relay/devnet/verify.js", "route": "npm run build && node node/dist/relay/devnet/route.js", "construct-proof": "npm run build && node node/dist/relay/devnet/proof.js", "approve": "npm run build && node node/dist/relay/devnet/approve.js", "execute": "npm run build && node node/dist/relay/devnet/execute.js", "build-and-start": "npm run build && npm run start", "clean": "rm -rf node/dist", "watch": "tsc --watch", "test": "forge test"}, "repository": {"type": "git", "url": "git+https://github.com/Telcoin-Association/tn-contracts.git"}, "keywords": ["Telcoin"], "author": "robriks", "license": "ISC", "bugs": {"url": "https://github.com/Telcoin-Association/tn-contracts/issues"}, "homepage": "https://github.com/Telcoin-Association/tn-contracts#readme", "engines": {"node": "18.x"}, "dependencies": {"@axelar-network/axelar-cgp-solidity": "^6.4.0", "@axelar-network/axelar-gmp-sdk-solidity": "^6.0.5", "@axelar-network/interchain-token-service": "^2.1.0", "@ethersproject/experimental": "^5.8.0", "@openzeppelin/contracts": "^5.2.0", "@openzeppelin/contracts-upgradeable": "^5.2.0", "@uniswap/v2-core": "^1.0.1", "@uniswap/v2-periphery": "^1.1.0-beta.0", "axios": "^1.7.7", "dotenv": "^16.4.5", "forge-std": "github:foundry-rs/forge-std", "safe-contracts": "github:safe-global/safe-contracts#v1.4.1-3", "js-yaml": "^4.1.0", "solady": "^0.0.227", "telcoin-contracts": "1.0.0", "viem": "^2.21.43"}, "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/node": "^20.14.11", "mermaid": "^11.4.0", "ts-node": "^10.9.2", "typescript": "^5.5.3"}}