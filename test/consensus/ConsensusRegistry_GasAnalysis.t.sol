// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ConsensusRegistryTestUtils } from "./ConsensusRegistryTestUtils.sol";
import { ConsensusRegistry } from "src/consensus/ConsensusRegistry.sol";
import { IConsensusRegistry } from "src/interfaces/IConsensusRegistry.sol";

/**
 * @title Gas Analysis: _getValidators Function Performance
 * @notice This test analyzes gas consumption of _getValidators with different validator counts
 */
contract ConsensusRegistry_GasAnalysis is Test, ConsensusRegistryTestUtils {
    address systemAddress;

    function setUp() public {
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);
        systemAddress = consensusRegistry.SYSTEM_ADDRESS();
    }

    function test_GasAnalysis_getValidators() public {
        console.log("=== Gas Analysis: getValidators Function ===");
        
        uint256[] memory validatorCounts = new uint256[](8);
        validatorCounts[0] = 100;
        validatorCounts[1] = 500;
        validatorCounts[2] = 1000;
        validatorCounts[3] = 2000;
        validatorCounts[4] = 3000;
        validatorCounts[5] = 5000;
        validatorCounts[6] = 10000;
        validatorCounts[7] = 15000;
        
        for (uint256 j = 0; j < validatorCounts.length; j++) {
            uint256 numValidators = validatorCounts[j];
            console.log("\n--- Testing with %s validators ---", numValidators);
            
            // Reset state for each test
            setUp();
            
            // Mint validators
            vm.startPrank(crOwner);
            for (uint256 i = 0; i < numValidators; i++) {
                address validator = makeAddr(string(abi.encodePacked("validator", j, "_", i)));
                consensusRegistry.mint(validator);
            }
            vm.stopPrank();
            
            console.log("Total supply: %s", consensusRegistry.totalSupply());
            
            // Measure gas for getValidators
            uint256 gasBefore = gasleft();
            try consensusRegistry.getValidators(IConsensusRegistry.ValidatorStatus.Active) {
                uint256 gasUsed = gasBefore - gasleft();
                console.log("Gas used for getValidators: %s", gasUsed);
                
                // Check if it would exceed block gas limit (30M)
                if (gasUsed > 30_000_000) {
                    console.log("WARNING: Gas usage exceeds typical block gas limit!");
                } else if (gasUsed > 15_000_000) {
                    console.log("CAUTION: Gas usage is high but within block limit");
                } else {
                    console.log("OK: Gas usage is reasonable");
                }
            } catch (bytes memory reason) {
                console.log("FAILURE: getValidators failed");
                console.logBytes(reason);
            }
        }
    }

    function test_GasAnalysis_concludeEpoch() public {
        console.log("=== Gas Analysis: concludeEpoch Function ===");
        
        uint256[] memory validatorCounts = new uint256[](6);
        validatorCounts[0] = 100;
        validatorCounts[1] = 500;
        validatorCounts[2] = 1000;
        validatorCounts[3] = 2000;
        validatorCounts[4] = 3000;
        validatorCounts[5] = 5000;
        
        for (uint256 j = 0; j < validatorCounts.length; j++) {
            uint256 numValidators = validatorCounts[j];
            console.log("\n--- Testing concludeEpoch with %s validators ---", numValidators);
            
            // Reset state for each test
            setUp();
            
            // Mint validators
            vm.startPrank(crOwner);
            for (uint256 i = 0; i < numValidators; i++) {
                address validator = makeAddr(string(abi.encodePacked("validator", j, "_", i)));
                consensusRegistry.mint(validator);
            }
            vm.stopPrank();
            
            console.log("Total supply: %s", consensusRegistry.totalSupply());
            
            // Measure gas for concludeEpoch
            address[] memory futureCommittee = _createTokenIdCommittee(4);
            
            uint256 gasBefore = gasleft();
            vm.prank(systemAddress);
            try consensusRegistry.concludeEpoch(futureCommittee) {
                uint256 gasUsed = gasBefore - gasleft();
                console.log("Gas used for concludeEpoch: %s", gasUsed);
                
                // Check if it would exceed block gas limit
                if (gasUsed > 30_000_000) {
                    console.log("CRITICAL: Gas usage exceeds block gas limit!");
                } else if (gasUsed > 15_000_000) {
                    console.log("WARNING: Gas usage is high");
                } else {
                    console.log("OK: Gas usage is reasonable");
                }
            } catch (bytes memory reason) {
                console.log("FAILURE: concludeEpoch failed");
                console.logBytes(reason);
            }
        }
    }

    function test_GasAnalysis_Comparison() public {
        console.log("=== Gas Analysis: Comparison Between Functions ===");
        
        uint256 numValidators = 1000;
        console.log("Testing with %s validators", numValidators);
        
        // Mint validators
        vm.startPrank(crOwner);
        for (uint256 i = 0; i < numValidators; i++) {
            address validator = makeAddr(string(abi.encodePacked("validator", i)));
            consensusRegistry.mint(validator);
        }
        vm.stopPrank();
        
        // Test getValidators gas
        uint256 gasBefore = gasleft();
        consensusRegistry.getValidators(IConsensusRegistry.ValidatorStatus.Active);
        uint256 getValidatorsGas = gasBefore - gasleft();
        
        // Test concludeEpoch gas
        address[] memory futureCommittee = _createTokenIdCommittee(4);
        gasBefore = gasleft();
        vm.prank(systemAddress);
        consensusRegistry.concludeEpoch(futureCommittee);
        uint256 concludeEpochGas = gasBefore - gasleft();
        
        console.log("getValidators gas: %s", getValidatorsGas);
        console.log("concludeEpoch gas: %s", concludeEpochGas);
        console.log("Difference: %s", concludeEpochGas - getValidatorsGas);
        console.log("getValidators percentage of concludeEpoch: %s%%", (getValidatorsGas * 100) / concludeEpochGas);
    }

    function test_GasAnalysis_WorstCase() public {
        console.log("=== Gas Analysis: Worst Case Scenario ===");
        
        // Test with maximum reasonable number of validators
        uint256 maxValidators = 20000;
        console.log("Testing worst case with %s validators", maxValidators);
        
        // Mint validators
        vm.startPrank(crOwner);
        for (uint256 i = 0; i < maxValidators; i++) {
            address validator = makeAddr(string(abi.encodePacked("validator", i)));
            consensusRegistry.mint(validator);
        }
        vm.stopPrank();
        
        console.log("Total supply: %s", consensusRegistry.totalSupply());
        
        // Test getValidators
        uint256 gasBefore = gasleft();
        try consensusRegistry.getValidators(IConsensusRegistry.ValidatorStatus.Active) {
            uint256 gasUsed = gasBefore - gasleft();
            console.log("getValidators gas with %s validators: %s", maxValidators, gasUsed);
            
            if (gasUsed > 30_000_000) {
                console.log("CONFIRMED: DoS vulnerability exists - exceeds block gas limit");
            } else {
                console.log("No DoS vulnerability at this scale");
            }
        } catch {
            console.log("getValidators failed - potential DoS confirmed");
        }
        
        // Test concludeEpoch
        address[] memory futureCommittee = _createTokenIdCommittee(4);
        gasBefore = gasleft();
        vm.prank(systemAddress);
        try consensusRegistry.concludeEpoch(futureCommittee) {
            uint256 gasUsed = gasBefore - gasleft();
            console.log("concludeEpoch gas with %s validators: %s", maxValidators, gasUsed);
            
            if (gasUsed > 30_000_000) {
                console.log("CONFIRMED: Network halt vulnerability exists");
            } else {
                console.log("No network halt vulnerability at this scale");
            }
        } catch {
            console.log("concludeEpoch failed - network halt vulnerability confirmed");
        }
    }

    function test_GasAnalysis_LinearGrowth() public {
        console.log("=== Gas Analysis: Linear Growth Pattern ===");
        
        uint256[] memory testSizes = new uint256[](5);
        testSizes[0] = 1000;
        testSizes[1] = 2000;
        testSizes[2] = 3000;
        testSizes[3] = 4000;
        testSizes[4] = 5000;
        
        uint256[] memory gasResults = new uint256[](testSizes.length);
        
        for (uint256 i = 0; i < testSizes.length; i++) {
            setUp();
            
            uint256 numValidators = testSizes[i];
            
            // Mint validators
            vm.startPrank(crOwner);
            for (uint256 j = 0; j < numValidators; j++) {
                address validator = makeAddr(string(abi.encodePacked("validator", i, "_", j)));
                consensusRegistry.mint(validator);
            }
            vm.stopPrank();
            
            // Measure gas
            uint256 gasBefore = gasleft();
            consensusRegistry.getValidators(IConsensusRegistry.ValidatorStatus.Active);
            gasResults[i] = gasBefore - gasleft();
            
            console.log("Validators: %s, Gas: %s", numValidators, gasResults[i]);
        }
        
        // Analyze growth pattern
        console.log("\n--- Growth Analysis ---");
        for (uint256 i = 1; i < testSizes.length; i++) {
            uint256 validatorIncrease = testSizes[i] - testSizes[i-1];
            uint256 gasIncrease = gasResults[i] - gasResults[i-1];
            uint256 gasPerValidator = gasIncrease / validatorIncrease;
            
            console.log("From %s to %s validators: %s gas per additional validator", 
                testSizes[i-1], testSizes[i], gasPerValidator);
        }
    }
}
