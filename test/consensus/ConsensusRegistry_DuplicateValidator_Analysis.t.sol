// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ConsensusRegistryTestUtils } from "./ConsensusRegistryTestUtils.sol";
import { ConsensusRegistry } from "src/consensus/ConsensusRegistry.sol";
import { RewardInfo } from "src/interfaces/IStakeManager.sol";

/**
 * @title Analysis: Duplicate Validator Attack in applyIncentives
 * @notice This test analyzes the claimed duplicate validator vulnerability
 */
contract ConsensusRegistry_DuplicateValidator_Analysis is Test, ConsensusRegistryTestUtils {
    address systemAddress;

    function setUp() public {
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);
        systemAddress = consensusRegistry.SYSTEM_ADDRESS();
        
        // Fund the issuance contract
        vm.deal(crOwner, epochIssuance_);
        vm.prank(crOwner);
        consensusRegistry.allocateIssuance{value: epochIssuance_}();
    }

    function test_DuplicateValidator_ExactScenario() public {
        console.log("=== Testing Exact Scenario from Report ===");
        
        uint256 headers = 100;
        
        // Calculate correct distribution (without duplicates)
        uint256 correctTotalWeight = (stakeAmount_ * headers) + (stakeAmount_ * headers);
        uint256 correctRewardPerValidator = (epochIssuance_ * (stakeAmount_ * headers)) / correctTotalWeight;
        
        console.log("Expected fair reward per validator: %s", correctRewardPerValidator);
        
        // Get initial rewards
        uint256 validator1_initialRewards = consensusRegistry.getRewards(validator1);
        uint256 validator2_initialRewards = consensusRegistry.getRewards(validator2);
        
        console.log("Validator 1 initial rewards: %s", validator1_initialRewards);
        console.log("Validator 2 initial rewards: %s", validator2_initialRewards);
        
        // Create malicious input with validator1 duplicated
        RewardInfo[] memory maliciousRewardInfos = new RewardInfo[](3);
        maliciousRewardInfos[0] = RewardInfo(validator1, headers);
        maliciousRewardInfos[1] = RewardInfo(validator2, headers);
        maliciousRewardInfos[2] = RewardInfo(validator1, headers); // Duplicate
        
        // Apply incentives
        vm.prank(systemAddress);
        consensusRegistry.applyIncentives(maliciousRewardInfos);
        
        // Get final rewards
        uint256 validator1_finalRewards = consensusRegistry.getRewards(validator1);
        uint256 validator2_finalRewards = consensusRegistry.getRewards(validator2);
        
        uint256 validator1_actualReward = validator1_finalRewards - validator1_initialRewards;
        uint256 validator2_actualReward = validator2_finalRewards - validator2_initialRewards;
        
        console.log("=== Results ===");
        console.log("Validator 1 actual reward: %s", validator1_actualReward);
        console.log("Validator 2 actual reward: %s", validator2_actualReward);
        console.log("Total distributed: %s", validator1_actualReward + validator2_actualReward);
        console.log("Expected total: %s", epochIssuance_);
        
        // Check if validator1 got more than fair share
        if (validator1_actualReward > correctRewardPerValidator) {
            console.log("CONFIRMED: Validator 1 received more than fair share");
            console.log("Excess amount: %s", validator1_actualReward - correctRewardPerValidator);
        } else {
            console.log("No unfair advantage detected");
        }
        
        // Check if validator2 got less than fair share
        if (validator2_actualReward < correctRewardPerValidator) {
            console.log("CONFIRMED: Validator 2 received less than fair share");
            console.log("Shortfall amount: %s", correctRewardPerValidator - validator2_actualReward);
        } else {
            console.log("Validator 2 received fair share");
        }
    }

    function test_DuplicateValidator_MathematicalAnalysis() public {
        console.log("=== Mathematical Analysis of Duplicate Attack ===");
        
        uint256 headers = 10;
        uint256 weight = stakeAmount_ * headers;
        
        // Normal case: 2 validators, no duplicates
        console.log("\n--- Normal Case ---");
        uint256 normalTotalWeight = weight + weight; // 2 * weight
        uint256 normalRewardPerValidator = (epochIssuance_ * weight) / normalTotalWeight;
        console.log("Normal total weight: %s", normalTotalWeight);
        console.log("Normal reward per validator: %s", normalRewardPerValidator);
        console.log("Normal percentage per validator: 50%%");
        
        // Attack case: validator1 appears twice
        console.log("\n--- Attack Case ---");
        uint256 attackTotalWeight = weight + weight + weight; // 3 * weight (validator1 counted twice)
        uint256 attackRewardValidator1 = 2 * ((epochIssuance_ * weight) / attackTotalWeight); // 2 entries
        uint256 attackRewardValidator2 = (epochIssuance_ * weight) / attackTotalWeight; // 1 entry
        
        console.log("Attack total weight: %s", attackTotalWeight);
        console.log("Attack reward for validator1: %s", attackRewardValidator1);
        console.log("Attack reward for validator2: %s", attackRewardValidator2);
        
        // Calculate percentages
        uint256 validator1Percentage = (attackRewardValidator1 * 100) / epochIssuance_;
        uint256 validator2Percentage = (attackRewardValidator2 * 100) / epochIssuance_;
        
        console.log("Validator1 percentage: %s%%", validator1Percentage);
        console.log("Validator2 percentage: %s%%", validator2Percentage);
        
        // Calculate theft amount
        uint256 stolenAmount = attackRewardValidator1 - normalRewardPerValidator;
        uint256 stolenPercentage = (stolenAmount * 10000) / normalRewardPerValidator; // basis points
        
        console.log("\n--- Theft Analysis ---");
        console.log("Amount stolen from validator2: %s", stolenAmount);
        console.log("Percentage stolen: %s basis points", stolenPercentage);
        
        // Verify the math matches the report's claim
        if (validator1Percentage == 66 && validator2Percentage == 33) {
            console.log("CONFIRMED: Attack gives validator1 66.67%% and validator2 33.33%%");
            console.log("This matches the report's mathematical analysis");
        }
    }

    function test_DuplicateValidator_MultipleScenarios() public {
        console.log("=== Testing Multiple Duplicate Scenarios ===");
        
        uint256 headers = 50;
        
        // Test different numbers of duplicates
        uint256[] memory duplicateCounts = new uint256[](4);
        duplicateCounts[0] = 1; // No duplicates (baseline)
        duplicateCounts[1] = 2; // 1 duplicate
        duplicateCounts[2] = 3; // 2 duplicates  
        duplicateCounts[3] = 5; // 4 duplicates
        
        for (uint256 d = 0; d < duplicateCounts.length; d++) {
            uint256 numDuplicates = duplicateCounts[d];
            console.log("\n--- Testing with %s entries for validator1 ---", numDuplicates);
            
            // Reset state
            setUp();
            
            // Create reward array
            RewardInfo[] memory rewardInfos = new RewardInfo[](numDuplicates + 1);
            
            // Add validator1 multiple times
            for (uint256 i = 0; i < numDuplicates; i++) {
                rewardInfos[i] = RewardInfo(validator1, headers);
            }
            // Add validator2 once
            rewardInfos[numDuplicates] = RewardInfo(validator2, headers);
            
            // Get initial rewards
            uint256 v1_initial = consensusRegistry.getRewards(validator1);
            uint256 v2_initial = consensusRegistry.getRewards(validator2);
            
            // Apply incentives
            vm.prank(systemAddress);
            consensusRegistry.applyIncentives(rewardInfos);
            
            // Calculate actual rewards
            uint256 v1_reward = consensusRegistry.getRewards(validator1) - v1_initial;
            uint256 v2_reward = consensusRegistry.getRewards(validator2) - v2_initial;
            
            // Calculate expected fair share (50% each)
            uint256 fairShare = epochIssuance_ / 2;
            
            console.log("Validator1 reward: %s", v1_reward);
            console.log("Validator2 reward: %s", v2_reward);
            console.log("Fair share would be: %s", fairShare);
            
            if (v1_reward > fairShare) {
                uint256 excess = v1_reward - fairShare;
                uint256 excessPercentage = (excess * 100) / fairShare;
                console.log("Validator1 excess: %s (%s%% more than fair)", excess, excessPercentage);
            }
            
            if (v2_reward < fairShare) {
                uint256 shortfall = fairShare - v2_reward;
                uint256 shortfallPercentage = (shortfall * 100) / fairShare;
                console.log("Validator2 shortfall: %s (%s%% less than fair)", shortfall, shortfallPercentage);
            }
        }
    }

    function test_DuplicateValidator_EdgeCases() public {
        console.log("=== Testing Edge Cases ===");
        
        // Test 1: Same validator with different header counts
        console.log("\n--- Test 1: Same validator with different header counts ---");
        RewardInfo[] memory rewardInfos1 = new RewardInfo[](3);
        rewardInfos1[0] = RewardInfo(validator1, 10);
        rewardInfos1[1] = RewardInfo(validator2, 10);
        rewardInfos1[2] = RewardInfo(validator1, 20); // Different header count
        
        uint256 v1_initial = consensusRegistry.getRewards(validator1);
        uint256 v2_initial = consensusRegistry.getRewards(validator2);
        
        vm.prank(systemAddress);
        consensusRegistry.applyIncentives(rewardInfos1);
        
        uint256 v1_reward = consensusRegistry.getRewards(validator1) - v1_initial;
        uint256 v2_reward = consensusRegistry.getRewards(validator2) - v2_initial;
        
        console.log("Validator1 reward (10+20 headers): %s", v1_reward);
        console.log("Validator2 reward (10 headers): %s", v2_reward);
        
        // Test 2: Multiple validators duplicated
        console.log("\n--- Test 2: Multiple validators duplicated ---");
        setUp(); // Reset
        
        RewardInfo[] memory rewardInfos2 = new RewardInfo[](4);
        rewardInfos2[0] = RewardInfo(validator1, 10);
        rewardInfos2[1] = RewardInfo(validator2, 10);
        rewardInfos2[2] = RewardInfo(validator1, 10); // Duplicate validator1
        rewardInfos2[3] = RewardInfo(validator2, 10); // Duplicate validator2
        
        v1_initial = consensusRegistry.getRewards(validator1);
        v2_initial = consensusRegistry.getRewards(validator2);
        
        vm.prank(systemAddress);
        consensusRegistry.applyIncentives(rewardInfos2);
        
        v1_reward = consensusRegistry.getRewards(validator1) - v1_initial;
        v2_reward = consensusRegistry.getRewards(validator2) - v2_initial;
        
        console.log("Validator1 reward (duplicated): %s", v1_reward);
        console.log("Validator2 reward (duplicated): %s", v2_reward);
        console.log("Both should receive equal amounts since both are duplicated equally");
        
        if (v1_reward == v2_reward) {
            console.log("CONFIRMED: Equal duplication results in equal rewards");
        } else {
            console.log("UNEXPECTED: Unequal rewards despite equal duplication");
        }
    }

    function test_DuplicateValidator_SystemCallOnly() public {
        console.log("=== Testing System Call Protection ===");
        
        RewardInfo[] memory rewardInfos = new RewardInfo[](2);
        rewardInfos[0] = RewardInfo(validator1, 10);
        rewardInfos[1] = RewardInfo(validator1, 10); // Duplicate
        
        // Test that non-system addresses cannot call applyIncentives
        vm.expectRevert();
        vm.prank(validator1);
        consensusRegistry.applyIncentives(rewardInfos);
        
        console.log("CONFIRMED: Only system address can call applyIncentives");
        
        // Test that system address can call it (even with duplicates)
        vm.prank(systemAddress);
        consensusRegistry.applyIncentives(rewardInfos);
        
        console.log("CONFIRMED: System address can call applyIncentives with duplicates");
    }
}
