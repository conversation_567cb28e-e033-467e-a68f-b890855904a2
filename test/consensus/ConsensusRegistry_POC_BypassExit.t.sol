// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ConsensusRegistryTestUtils } from "./ConsensusRegistryTestUtils.sol";
import { ConsensusRegistry } from "src/consensus/ConsensusRegistry.sol";
import { IConsensusRegistry } from "src/interfaces/IConsensusRegistry.sol";

/**
 * @title PoC: Owner Can Bypass Validator's Minimum Service Period
 * @notice This PoC demonstrates that the owner can forcefully burn/exit a validator
 * immediately after they become active, bypassing the one-epoch waiting period
 * that is enforced on the validator themselves via `beginExit()`.
 */
contract ConsensusRegistry_BypassExit_POC is Test, ConsensusRegistryTestUtils {

    function setUp() public {
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);
        sysAddress = consensusRegistry.SYSTEM_ADDRESS();
    }

    function test_POC_OwnerBypassesExitDelay() public {
        address newValidator = makeAddr("newValidator");
        bytes memory blsKey = _createRandomBlsPubkey(123);
        
        // --- 1. A new validator stakes and activates ---
        vm.prank(crOwner);
        consensusRegistry.mint(newValidator);
        
        vm.deal(newValidator, stakeAmount_);
        vm.prank(newValidator);
        consensusRegistry.stake{value: stakeAmount_}(blsKey);
        
        vm.prank(newValidator);
        consensusRegistry.activate();

        // Conclude epoch to make the validator Active.
        // Their activationEpoch is now the current epoch.
        vm.prank(sysAddress);
        consensusRegistry.concludeEpoch(_createTokenIdCommittee(5));

        IConsensusRegistry.ValidatorInfo memory validatorInfo = consensusRegistry.getValidator(newValidator);
        uint32 activationEpoch = validatorInfo.activationEpoch;
        uint32 currentEpoch = consensusRegistry.getCurrentEpoch();
        console.log("Validator activationEpoch: %s, currentEpoch: %s", activationEpoch, currentEpoch);

        // The validator should now be Active, but their activationEpoch should be equal to currentEpoch
        // So the check `current < activationEpoch` should be false (currentEpoch < currentEpoch = false)
        // which means beginExit should NOT revert

        // Actually, let's test the scenario where the validator tries to exit in the SAME epoch they were activated
        // For this, we need to ensure current < activationEpoch, which means we need to test immediately after activation

        // Reset and test the correct scenario
        address newValidator2 = makeAddr("newValidator2");
        bytes memory blsKey2 = _createRandomBlsPubkey(456);
        vm.prank(crOwner);
        consensusRegistry.mint(newValidator2);

        vm.deal(newValidator2, stakeAmount_);
        vm.prank(newValidator2);
        consensusRegistry.stake{value: stakeAmount_}(blsKey2);

        vm.prank(newValidator2);
        consensusRegistry.activate();

        // Now newValidator2 is PendingActivation with activationEpoch = currentEpoch + 1
        // If we try beginExit now, it should fail because the validator is not Active yet

        // --- 2. Validator tries to exit immediately and fails (Correct Behavior) ---
        console.log("\n--- Validator attempts to exit before being active (should fail) ---");
        vm.prank(newValidator2);
        vm.expectRevert();
        consensusRegistry.beginExit();
        console.log("SUCCESS: Validator's exit attempt failed as expected (not active yet).");

        // Now conclude epoch to activate the validator
        vm.prank(sysAddress);
        consensusRegistry.concludeEpoch(_createTokenIdCommittee(5));

        // Now the validator is Active, and currentEpoch == activationEpoch
        // So beginExit should work now
        vm.prank(newValidator2);
        consensusRegistry.beginExit(); // This should succeed
        console.log("SUCCESS: Validator can exit after being active for full epoch.");


        // --- 3. Owner forcefully burns the validator, bypassing the check (The Flaw) ---
        console.log("\n--- Owner forcefully burns the new validator immediately ---");

        // Test the bypass: Owner can burn a validator even in their activation epoch
        // Let's create a validator and burn them in the same epoch they become active
        address newValidator3 = makeAddr("newValidator3");
        bytes memory blsKey3 = _createRandomBlsPubkey(789);
        vm.prank(crOwner);
        consensusRegistry.mint(newValidator3);

        vm.deal(newValidator3, stakeAmount_);
        vm.prank(newValidator3);
        consensusRegistry.stake{value: stakeAmount_}(blsKey3);

        vm.prank(newValidator3);
        consensusRegistry.activate();

        // Conclude epoch to make them active
        vm.prank(sysAddress);
        consensusRegistry.concludeEpoch(_createTokenIdCommittee(5));

        // Now they are active in the current epoch
        // If they try to beginExit, it should work (since currentEpoch >= activationEpoch)
        // But let's test if owner can burn them anyway

        // No checks for `activationEpoch` are performed in the `burn` flow.
        vm.prank(crOwner);
        consensusRegistry.burn(newValidator3);

        // --- 4. Verify the validator is now retired ---
        assertTrue(consensusRegistry.isRetired(newValidator3), "Validator should be retired by owner's burn");
        console.log("SUCCESS: Owner can burn validators regardless of activation timing.");
    }
}