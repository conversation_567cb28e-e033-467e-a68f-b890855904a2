// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity ^0.8.24;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import { GitAttestationRegistry } from "../../src/CI/GitAttestationRegistry.sol";

/**
 * @title PoC: Logic Error in setBufferSize leads to unusable contract state
 * @notice This PoC demonstrates that `setBufferSize` fails to correctly populate the
 * new buffer, leading to a mismatch between `bufferSize` variable and `ringBuffer.length`.
 * This causes all subsequent calls to `gitCommitHashAttested` to fail with an out-of-bounds error.
 */
contract GitAttestationRegistry_InvalidState_POC is Test {
    GitAttestationRegistry gitAttestationRegistry;
    address admin;
    address maintainer = address(0x123);

    function setUp() public {
        admin = address(this);
        address[] memory maintainers = new address[](2);
        maintainers[0] = admin;
        maintainers[1] = maintainer;
        gitAttestationRegistry = new GitAttestationRegistry(10, maintainers);

        // Fill the buffer with some data
        vm.prank(maintainer);
        for(uint8 i = 0; i < 10; i++) {
            gitAttestationRegistry.attestGitCommitHash(bytes20(uint160(i+1)), true);
        }
    }

    function test_POC_SetBufferSize_Causes_Permanent_DoS() public {
        // --- 1. Verify the contract is working normally before the attack ---
        bool isAttestedBefore = gitAttestationRegistry.gitCommitHashAttested(bytes20(uint160(5)));
        assertTrue(isAttestedBefore, "Contract should be functional before resizing");

        // --- 2. Admin calls setBufferSize with a larger size ---
        uint8 newSize = 20;
        vm.prank(admin);
        gitAttestationRegistry.setBufferSize(newSize);

        // --- 3. VERIFY: Any subsequent call to the getter function now reverts with a panic ---
        // The loop in `gitCommitHashAttested` runs up to `bufferSize` (which is 20),
        // but the actual array `ringBuffer` only has `itemsToCopy` (10) elements.
        // Accessing index 10 will cause an out-of-bounds panic (code 0x32).
        bytes20 randomHash = bytes20(keccak256("test"));

        // @notice We must specifically expect a revert with the panic code for 'array out-of-bounds'.
        vm.expectRevert(abi.encodePacked(bytes4(0x4e487b71), uint256(0x32)));
        gitAttestationRegistry.gitCommitHashAttested(randomHash);
    }

    function test_Debug_Buffer_State() public {
        console.log("=== Initial State ===");
        console.log("bufferSize:", gitAttestationRegistry.bufferSize());

        // Check actual array length by trying to access elements
        uint8 actualLength = 0;
        for (uint8 i = 0; i < 255; i++) {
            try gitAttestationRegistry.ringBuffer(i) returns (bytes20, bool) {
                actualLength = i + 1;
            } catch {
                break;
            }
        }
        console.log("Actual ringBuffer length:", actualLength);

        // Resize to larger size
        uint8 newSize = 20;
        vm.prank(admin);
        gitAttestationRegistry.setBufferSize(newSize);

        console.log("=== After Resize ===");
        console.log("bufferSize:", gitAttestationRegistry.bufferSize());

        // Check actual array length again
        actualLength = 0;
        for (uint8 i = 0; i < 255; i++) {
            try gitAttestationRegistry.ringBuffer(i) returns (bytes20, bool) {
                actualLength = i + 1;
            } catch {
                break;
            }
        }
        console.log("Actual ringBuffer length after resize:", actualLength);

        // This should demonstrate the mismatch
        assertTrue(gitAttestationRegistry.bufferSize() != actualLength, "Buffer size mismatch detected");
    }
}