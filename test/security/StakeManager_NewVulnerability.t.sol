// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ConsensusRegistryTestUtils } from "../consensus/ConsensusRegistryTestUtils.sol";
import { ConsensusRegistry } from "src/consensus/ConsensusRegistry.sol";
import { IConsensusRegistry } from "src/interfaces/IConsensusRegistry.sol";
import { IStakeManager, RewardInfo } from "src/interfaces/IStakeManager.sol";

/**
 * @title StakeManager New Vulnerability Analysis
 * @notice Testing for undiscovered vulnerabilities in StakeManager
 */
contract StakeManager_NewVulnerability is Test, ConsensusRegistryTestUtils {
    address systemAddress;
    address attacker;
    address victim;

    function setUp() public {
        StakeConfig memory stakeConfig_ = StakeConfig(stakeAmount_, minWithdrawAmount_, epochIssuance_, epochDuration_);
        consensusRegistry = new ConsensusRegistry(stakeConfig_, initialValidators, crOwner);
        systemAddress = consensusRegistry.SYSTEM_ADDRESS();
        
        attacker = makeAddr("attacker");
        victim = makeAddr("victim");
        
        // Fund the issuance contract
        vm.deal(crOwner, epochIssuance_);
        vm.prank(crOwner);
        consensusRegistry.allocateIssuance{value: epochIssuance_}();
    }

    function test_Vulnerability_StakeVersionArbitrage() public {
        console.log("=== Testing Stake Version Arbitrage ===");
        
        // Create validators with current version
        vm.prank(crOwner);
        consensusRegistry.mint(victim);
        
        vm.prank(crOwner);
        consensusRegistry.mint(attacker);
        
        // Both stake with current version
        vm.deal(victim, stakeAmount_);
        vm.prank(victim);
        bytes memory blsPubkey1 = new bytes(96);
        for (uint i = 0; i < 96; i++) {
            blsPubkey1[i] = bytes1(uint8(i + 1));
        }
        consensusRegistry.stake{value: stakeAmount_}(blsPubkey1);
        
        vm.deal(attacker, stakeAmount_);
        vm.prank(attacker);
        bytes memory blsPubkey2 = new bytes(96);
        for (uint i = 0; i < 96; i++) {
            blsPubkey2[i] = bytes1(uint8(i + 2));
        }
        consensusRegistry.stake{value: stakeAmount_}(blsPubkey2);
        
        console.log("Both validators staked with version 0");
        
        // Owner creates new version with higher stake amount
        StakeConfig memory newConfig = StakeConfig(
            stakeAmount_ * 2, // Double stake amount
            minWithdrawAmount_,
            epochIssuance_ * 2, // Double issuance
            epochDuration_
        );
        
        vm.prank(crOwner);
        uint8 newVersion = consensusRegistry.upgradeStakeVersion(newConfig);
        
        console.log("New stake version %s created with 2x stake and 2x issuance", newVersion);
        
        // Apply rewards to both validators
        RewardInfo[] memory rewardInfos = new RewardInfo[](2);
        rewardInfos[0] = RewardInfo(victim, 100);
        rewardInfos[1] = RewardInfo(attacker, 100);
        
        vm.prank(systemAddress);
        consensusRegistry.applyIncentives(rewardInfos);
        
        uint256 victimReward = consensusRegistry.getBalance(victim) - stakeAmount_;
        uint256 attackerReward = consensusRegistry.getBalance(attacker) - stakeAmount_;
        
        console.log("Victim reward (old version): %s", victimReward);
        console.log("Attacker reward (old version): %s", attackerReward);
        
        // Check if rewards are equal (they should be)
        if (victimReward == attackerReward) {
            console.log("EXPECTED: Equal rewards for equal performance");
        } else {
            console.log("POTENTIAL ISSUE: Unequal rewards despite equal performance");
        }
    }

    function test_Vulnerability_DelegationNonceManipulation() public {
        console.log("=== Testing Delegation Nonce Manipulation ===");
        
        // Create validator
        vm.prank(crOwner);
        consensusRegistry.mint(victim);
        
        // Get initial delegation digest
        bytes memory blsPubkey = new bytes(96);
        for (uint i = 0; i < 96; i++) {
            blsPubkey[i] = bytes1(uint8(i + 1));
        }
        
        bytes32 digest1 = consensusRegistry.delegationDigest(blsPubkey, victim, attacker);
        console.log("First delegation digest created");
        
        // Try to get same digest again (should be same since nonce hasn't changed)
        bytes32 digest2 = consensusRegistry.delegationDigest(blsPubkey, victim, attacker);
        
        if (digest1 == digest2) {
            console.log("EXPECTED: Same digest for same parameters");
        } else {
            console.log("UNEXPECTED: Different digests for same parameters");
        }
        
        // Test if nonce increments properly after actual delegation
        console.log("Testing nonce increment mechanism");
    }

    function test_Vulnerability_IssuanceAllocationManipulation() public {
        console.log("=== Testing Issuance Allocation Manipulation ===");
        
        // Check initial issuance balance
        uint256 initialBalance = address(consensusRegistry.issuance()).balance;
        console.log("Initial issuance balance: %s", initialBalance);
        
        // Owner allocates more issuance
        vm.deal(crOwner, 1000 ether);
        vm.prank(crOwner);
        consensusRegistry.allocateIssuance{value: 500 ether}();
        
        uint256 newBalance = address(consensusRegistry.issuance()).balance;
        console.log("New issuance balance: %s", newBalance);
        
        // Check if allocation worked
        if (newBalance > initialBalance) {
            console.log("Issuance allocation successful");
            console.log("Increase: %s", newBalance - initialBalance);
        }
        
        // Try to allocate from non-owner (should fail)
        vm.deal(attacker, 1000 ether);
        vm.prank(attacker);
        vm.expectRevert();
        consensusRegistry.allocateIssuance{value: 100 ether}();
        
        console.log("EXPECTED: Non-owner allocation rejected");
        
        // Test if issuance can be drained through rewards
        console.log("Testing if issuance can be drained");
        
        // Create many validators and give them huge rewards
        address[] memory manyValidators = new address[](10);
        for (uint i = 0; i < 10; i++) {
            manyValidators[i] = makeAddr(string(abi.encodePacked("validator", i)));
            vm.prank(crOwner);
            consensusRegistry.mint(manyValidators[i]);
            
            // Stake
            vm.deal(manyValidators[i], stakeAmount_);
            vm.prank(manyValidators[i]);
            bytes memory blsPubkey = new bytes(96);
            for (uint j = 0; j < 96; j++) {
                blsPubkey[j] = bytes1(uint8(j + i + 1));
            }
            consensusRegistry.stake{value: stakeAmount_}(blsPubkey);
        }
        
        // Apply massive rewards
        RewardInfo[] memory massiveRewards = new RewardInfo[](10);
        for (uint i = 0; i < 10; i++) {
            massiveRewards[i] = RewardInfo(manyValidators[i], 1000); // High header count
        }
        
        vm.prank(systemAddress);
        consensusRegistry.applyIncentives(massiveRewards);
        
        uint256 finalIssuanceBalance = address(consensusRegistry.issuance()).balance;
        console.log("Issuance balance after massive rewards: %s", finalIssuanceBalance);
        
        if (finalIssuanceBalance < newBalance) {
            console.log("Issuance decreased by: %s", newBalance - finalIssuanceBalance);
        }
    }

    function test_Vulnerability_TokenIdCollisionAttack() public {
        console.log("=== Testing Token ID Collision Attack ===");
        
        // Test with edge case addresses
        address maxAddress = address(type(uint160).max);
        address nearMaxAddress = address(type(uint160).max - 1);
        
        console.log("Max address: %s", maxAddress);
        console.log("Near max address: %s", nearMaxAddress);
        
        // Try to mint to max address
        vm.prank(crOwner);
        try consensusRegistry.mint(maxAddress) {
            console.log("Max address mint succeeded");
            
            uint256 tokenId = uint160(maxAddress);
            console.log("Token ID: %s", tokenId);
            
            // Check if token exists
            bool exists = consensusRegistry.ownerOf(tokenId) == maxAddress;
            console.log("Token exists: %s", exists);
            
        } catch (bytes memory reason) {
            console.log("Max address mint failed");
            console.logBytes(reason);
        }
        
        // Try to mint to near max address
        vm.prank(crOwner);
        try consensusRegistry.mint(nearMaxAddress) {
            console.log("Near max address mint succeeded");
        } catch {
            console.log("Near max address mint failed");
        }
        
        // Test potential collision with contract addresses
        address contractAddress = address(consensusRegistry);
        console.log("Contract address: %s", contractAddress);
        
        vm.prank(crOwner);
        vm.expectRevert();
        consensusRegistry.mint(contractAddress);
        
        console.log("EXPECTED: Cannot mint to contract address");
    }

    function test_Vulnerability_RewardCalculationOverflow() public {
        console.log("=== Testing Reward Calculation Overflow ===");
        
        // Create validator
        vm.prank(crOwner);
        consensusRegistry.mint(victim);
        
        // Stake
        vm.deal(victim, stakeAmount_);
        vm.prank(victim);
        bytes memory blsPubkey = new bytes(96);
        for (uint i = 0; i < 96; i++) {
            blsPubkey[i] = bytes1(uint8(i + 1));
        }
        consensusRegistry.stake{value: stakeAmount_}(blsPubkey);
        
        // Try to apply rewards with maximum values
        RewardInfo[] memory maxRewards = new RewardInfo[](1);
        maxRewards[0] = RewardInfo(victim, type(uint256).max);
        
        console.log("Attempting to apply maximum header count");
        
        vm.prank(systemAddress);
        try consensusRegistry.applyIncentives(maxRewards) {
            console.log("Maximum rewards applied successfully");
            
            uint256 balance = consensusRegistry.getBalance(victim);
            console.log("Victim balance after max rewards: %s", balance);
            
        } catch (bytes memory reason) {
            console.log("Maximum rewards failed");
            console.logBytes(reason);
        }
        
        // Test with large but reasonable values
        RewardInfo[] memory largeRewards = new RewardInfo[](1);
        largeRewards[0] = RewardInfo(victim, 1e18);
        
        vm.prank(systemAddress);
        try consensusRegistry.applyIncentives(largeRewards) {
            console.log("Large rewards applied successfully");
        } catch {
            console.log("Large rewards failed");
        }
    }

    function test_Vulnerability_GenesisValidatorPrivileges() public {
        console.log("=== Testing Genesis Validator Privileges ===");
        
        // Check if genesis validators have special privileges
        address genesisValidator = initialValidators[0].validatorAddress;
        console.log("Genesis validator: %s", genesisValidator);
        
        // Check validator status
        IConsensusRegistry.ValidatorInfo memory validatorInfo = consensusRegistry.getValidator(genesisValidator);
        console.log("Genesis validator status: %s", uint(validatorInfo.currentStatus));
        console.log("Genesis validator stake version: %s", validatorInfo.stakeVersion);
        
        // Check if genesis validator can be burned
        vm.prank(crOwner);
        try consensusRegistry.burn(genesisValidator) {
            console.log("CRITICAL: Genesis validator was burned!");
        } catch {
            console.log("EXPECTED: Genesis validator cannot be burned");
        }
        
        // Check if genesis validator can unstake
        vm.prank(genesisValidator);
        try consensusRegistry.unstake(genesisValidator) {
            console.log("Genesis validator unstaked successfully");
        } catch {
            console.log("Genesis validator cannot unstake");
        }
    }
}
