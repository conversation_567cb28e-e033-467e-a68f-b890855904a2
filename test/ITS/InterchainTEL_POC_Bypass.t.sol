// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { ITSTestHelper } from "./ITSTestHelper.sol";
import { console } from "forge-std/console.sol";

/**
 * @title PoC: 7-Day Outbound Cool-down is Bypassed via Direct WTEL Interaction
 * @notice This PoC demonstrates that the protocol's core security promise of a 7-day
 * wait period for outbound flows is completely nullified because the WTEL contract
 * allows for public, instant deposit and withdrawal of the underlying native TEL.
 */
contract InterchainTEL_Bypass_POC is ITSTestHelper {
    address user = makeAddr("user");
    address admin = makeAddr("admin");

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
    }

    function test_POC_BypassOutboundDelay() public {
        uint256 amount = 10 ether;
        vm.deal(user, amount * 2); // Fund user with enough for both paths

        // --- Path 1: The Intended, Slow Path (via InterchainTEL) ---
        console.log("--- Testing Path 1: Intended Slow Path via iTEL ---");
        
        vm.startPrank(user);
        // User wraps TEL to iTEL, triggering the 7-day delay
        iTEL.doubleWrap{value: amount}();
        
        // Verify user has an unsettled balance
        uint256 unsettledBalance = iTEL.balanceOf(user, true) - iTEL.balanceOf(user, false);
        assertEq(unsettledBalance, amount, "User should have unsettled iTEL");

        // Prove that immediate withdrawal is blocked
        vm.expectRevert();
        iTEL.unwrap(amount);
        console.log("SUCCESS: Immediate unwrap via iTEL failed as expected.");
        vm.stopPrank();

        // --- Path 2: The Bypass, Instant Path (via WTEL directly) ---
        console.log("\n--- Testing Path 2: Instant Bypass Path via WTEL ---");
        
        uint256 user_balance_before = user.balance;

        // User directly deposits native TEL into WTEL, bypassing iTEL and the delay
        vm.prank(user);
        wTEL.deposit{value: amount}();
        
        // Verify user now has liquid WTEL
        assertEq(wTEL.balanceOf(user), amount, "User should have liquid wTEL");
        console.log("SUCCESS: User instantly received liquid WTEL.");

        // User immediately withdraws their native TEL
        vm.prank(user);
        wTEL.withdraw(amount);
        vm.stopPrank();

        // Verify user got their native TEL back instantly
        assertEq(wTEL.balanceOf(user), 0, "User's WTEL balance should be zero");
        assertEq(user.balance, user_balance_before, "User's native TEL balance should be restored");
        console.log("SUCCESS: User instantly withdrew native TEL, bypassing the 7-day delay.");
    }
}