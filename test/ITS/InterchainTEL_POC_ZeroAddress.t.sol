// SPDX-License-Identifier: MIT or Apache-2.0
pragma solidity 0.8.26;

import { Test, console } from "forge-std/Test.sol";
import { ITSTestHelper } from "./ITSTestHelper.sol";
import { IInterchainTEL } from "src/interfaces/IInterchainTEL.sol";
import { InterchainTEL } from "src/InterchainTEL.sol";

/**
 * @title PoC: Funds Locked via permitWrap to Zero Address
 * @notice This PoC demonstrates that a user's funds could be lost forever by calling
 * `permitWrap` with a crafted signature for the zero address, because the
 * function lacks an explicit zero-address check.
 */
contract InterchainTEL_PermitZeroAddress_POC is ITSTestHelper {
    address user = makeAddr("user");
    address admin = makeAddr("admin");
    uint256 attackerPrivateKey = 0xBAD;
    address attackerSigner = vm.addr(attackerPrivateKey);

    function setUp() public {
        setUp_tnFork_devnetConfig_create3(admin, MAINNET_TEL);
    }

    function test_POC_LockFundsViaPermitWrapToZeroAddress() public {
        uint256 wrapAmount = 100 * 1e18;
        vm.deal(user, wrapAmount); // The user who will pay

        // --- 1. Attempt to call permitWrap with zero address as owner ---
        // The actual permitWrap function signature is:
        // permitWrap(address owner, uint256 amount, uint256 deadline, uint8 v, bytes32 r, bytes32 s)

        uint256 deadline = block.timestamp + 1 hours;

        // Create invalid signature components for the zero address
        uint8 v = 0;
        bytes32 r = bytes32(0);
        bytes32 s = bytes32(0);

        // --- 2. The user is tricked into calling permitWrap with zero address ---

        // We expect the call to revert because the signature is invalid for the zero address.
        // The CRITICAL finding is that the function proceeds to the signature verification
        // instead of reverting immediately with a "ZeroAddress" error. This proves the
        // absence of a preventative zero-address check.
        vm.expectRevert(); // Expect any revert due to invalid signature

        vm.prank(user);
        iTEL.permitWrap(address(0), wrapAmount, deadline, v, r, s);
    }
}