
# Finding: Unbounded Loop in `_unsettledBalanceOf` Allows Gas Exhaustion DoS, Permanently Freezing User Funds

## Summary
A malicious actor can permanently freeze any user's funds within the `InterchainTEL` contract by exploiting an unbounded loop in the `_unsettledBalanceOf` view function. By sending a large number of small "dust" transfers to a victim, an attacker can inflate the number of "unsettled" records associated with the victim's account. When the victim attempts to perform any action that requires calculating their spendable settled balance (e.g., `burn()` to bridge tokens), the gas cost of iterating through these records will exceed the block gas limit, causing the transaction to fail every time. This provides no path for the user to recover their funds.

## Finding Description
The `burn()` function in `InterchainTEL.sol` is the entry point for users to bridge their iTEL tokens to other chains. This function calls `_burnSettled()` in the inherited `RecoverableWrapper.sol` contract.

Before burning, `_burnSettled()` must calculate the user's spendable settled balance to ensure they have sufficient funds. It does this by calling the internal view function `_unsettledBalanceOf(account)`.

The `_unsettledBalanceOf` function contains two loops that iterate over an account's unsettled records. The first of these loops is unbounded and is the source of the vulnerability:

```solidity
// RecoverableWrapper.sol#L303-L313
function _unsettledBalanceOf(address account)
    private
    view
    returns (uint256 unsettledTotal, uint256 unsettledFrozen)
{
    // ...
    uint256 current = _unsettledRecords[account].tail;
    while (current > cacheIndex) { // <-- UNBOUNDED LOOP
        Record memory r = _unsettledRecords[account].getAt(current);
        if (r.settlementTime <= block.timestamp) break;
        unsettledTotal += r.amount;
        unsettledFrozen += r.frozen;
        current = r.prev;
    }
    // ...
}
```

This `while` loop iterates backwards from the most recent record (`tail`) until it reaches the last cached record (`cacheIndex`). An attacker can create an arbitrarily large number of new, uncached records by simply sending dust transfers to the victim. Each transfer calls `_transfer()`, which enqueues a new record for the recipient: `_unsettledRecords[to].enqueue(...)`.

If an attacker creates thousands of such records, any subsequent call to `_unsettledBalanceOf` by the victim will run out of gas while executing this loop, causing the parent `burn()` transaction to revert. Since the user cannot prevent incoming transfers and has no other mechanism to clear these records, their funds that require a settled balance check become permanently trapped.

## Impact
This vulnerability leads to a **permanent and irrecoverable loss of funds** for any targeted user. An attacker can freeze the assets of any user, including high-value addresses like exchange wallets or protocol treasuries, at a relatively low cost. This breaks the core functionality of the bridge and undermines the fundamental trust in the protocol.

## Likelihood
High. The attack vector is straightforward, requires no special permissions, and is inexpensive to execute. Any user can attack any other user.

## Proof of Concept
A Foundry test can demonstrate this vulnerability:
1.  **Setup:** Create an `attacker` and a `victim` account, both with a balance of iTEL.
2.  **Attack:** In a loop, have the `attacker` send 2,000 separate transactions of `1 wei` of iTEL to the `victim`. This populates the victim's `_unsettledRecords` deque with 2,000 new records.
3.  **Verification:**
    *   First, confirm the `victim` can successfully call `burn()` *before* the attack.
    *   After the attack, have the `victim` attempt to call `burn()` on their settled balance.
    *   The transaction will fail. Use `vm.expectRevert()` to confirm the transaction reverts due to running out of gas. The victim's funds are now permanently frozen.

## Recommendation
The accounting model for unsettled balances must be refactored to avoid unbounded loops. Instead of calculating the total unsettled amount on-the-fly, it should be stored and managed as a state variable.

1.  Add a new state variable, `uint128 unsettledBalance`, to the `AccountState` struct.
2.  **On mint/transfer:** When a user receives funds (`_mintUnsettled`, `_transfer`), increment this `unsettledBalance` variable by the amount received.
3.  **On settlement (`_clean`):** When a record is settled and removed from the deque, decrement the `unsettledBalance` by the record's amount.

This changes the balance calculation from a gas-intensive O(N) operation to a simple O(1) state read, completely mitigating the DoS vector.

## Severity Justification
The severity is **High/Critical**. It leads to a permanent loss of user funds, which is the most severe impact on users. The attack is simple and cheap, making it highly likely to be exploited. It breaks a core promise of the protocol—the ability to bridge assets out.

## Conclusion
The unbounded loop in `_unsettledBalanceOf` is a critical design flaw that creates a potent DoS vulnerability. It is essential to implement the recommended accounting refactor to protect user funds and ensure the reliability of the Telcoin bridge.
